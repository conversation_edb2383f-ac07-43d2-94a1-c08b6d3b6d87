<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];
$message = '';
$success = '';

// Handle sale submission
if ($_POST && $_POST['action'] === 'process_sale') {
    $customer_name = sanitize_input($_POST['customer_name'] ?? '');
    $customer_phone = sanitize_input($_POST['customer_phone'] ?? '');
    $payment_method = sanitize_input($_POST['payment_method']);
    $discount_amount = (float)($_POST['discount_amount'] ?? 0);
    $notes = sanitize_input($_POST['notes'] ?? '');
    $cart_items = json_decode($_POST['cart_items'], true);
    
    if (empty($cart_items)) {
        $message = 'কার্টে কোন পণ্য নেই।';
    } else {
        try {
            $pdo->beginTransaction();
            
            // Calculate totals
            $total_amount = 0;
            foreach ($cart_items as $item) {
                $total_amount += $item['price'] * $item['quantity'];
            }
            $final_amount = $total_amount - $discount_amount;
            
            // Generate sale number
            $sale_number = 'POS' . date('Ymd') . sprintf('%04d', rand(1, 9999));
            
            // Insert sale
            $stmt = $pdo->prepare("INSERT INTO pos_sales (sale_number, salesman_id, customer_name, customer_phone, total_amount, discount_amount, final_amount, payment_method, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$sale_number, $salesman_id, $customer_name, $customer_phone, $total_amount, $discount_amount, $final_amount, $payment_method, $notes]);
            
            $sale_id = $pdo->lastInsertId();
            
            // Insert sale items and update stock
            foreach ($cart_items as $item) {
                // Insert sale item
                $stmt = $pdo->prepare("INSERT INTO pos_sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$sale_id, $item['id'], $item['quantity'], $item['price'], $item['price'] * $item['quantity']]);
                
                // Update product stock
                $stmt = $pdo->prepare("UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?");
                $stmt->execute([$item['quantity'], $item['id']]);
            }
            
            $pdo->commit();
            
            log_activity('pos_sale', "POS Sale: $sale_number", $salesman_id, 'salesman');
            $success = "বিক্রয় সফল! বিক্রয় নম্বর: $sale_number";
            
        } catch(PDOException $e) {
            $pdo->rollBack();
            $message = 'বিক্রয় প্রক্রিয়াকরণে সমস্যা: ' . $e->getMessage();
        }
    }
}

// Get products for search
try {
    $stmt = $pdo->prepare("SELECT p.*, c.name as category_name FROM products p 
                          LEFT JOIN categories c ON p.category_id = c.id 
                          WHERE p.status = 'active' AND p.stock_quantity > 0 
                          ORDER BY p.name ASC");
    $stmt->execute();
    $products = $stmt->fetchAll();
} catch(PDOException $e) {
    $message = 'পণ্য লোড করতে সমস্যা: ' . $e->getMessage();
    $products = [];
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS সিস্টেম - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .pos-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 1rem;
            padding: 1rem 0;
            height: calc(100vh - 140px);
        }
        .products-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .search-bar {
            padding: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        .search-bar input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }
        .products-grid {
            padding: 1rem;
            height: calc(100vh - 220px);
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        .product-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .product-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .product-card h4 {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        .product-price {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .product-stock {
            font-size: 0.8rem;
            color: #666;
        }
        .cart-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        .cart-header {
            padding: 1rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .cart-items {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .item-info {
            flex: 1;
        }
        .item-name {
            font-weight: 500;
            font-size: 0.9rem;
        }
        .item-price {
            color: #666;
            font-size: 0.8rem;
        }
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .quantity-input {
            width: 50px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 0.25rem;
        }
        .remove-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8rem;
        }
        .cart-summary {
            padding: 1rem;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .summary-total {
            font-weight: bold;
            font-size: 1.1rem;
            border-top: 1px solid #ddd;
            padding-top: 0.5rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        .form-control {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .alert {
            padding: 0.75rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media (max-width: 1024px) {
            .pos-container {
                grid-template-columns: 1fr;
                height: auto;
            }
            .products-grid {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php" class="active"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
            </ul>
        </div>
    </nav>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $success; ?>
            </div>
        <?php endif; ?>

        <div class="pos-container">
            <!-- Products Section -->
            <div class="products-section">
                <div class="search-bar">
                    <input type="text" id="product-search" placeholder="পণ্য খুঁজুন..." onkeyup="searchProducts()">
                </div>
                <div class="products-grid" id="products-grid">
                    <?php foreach ($products as $product): ?>
                        <div class="product-card" onclick="addToCart(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                            <i class="fas fa-box fa-2x" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                            <h4><?php echo htmlspecialchars($product['name']); ?></h4>
                            <div class="product-price">৳<?php echo number_format($product['discount_price'] ?: $product['price'], 2); ?></div>
                            <div class="product-stock">স্টক: <?php echo $product['stock_quantity']; ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Cart Section -->
            <div class="cart-section">
                <div class="cart-header">
                    <h3><i class="fas fa-shopping-cart"></i> বিক্রয় কার্ট</h3>
                </div>

                <div class="cart-items" id="cart-items">
                    <div style="text-align: center; color: #666; padding: 2rem;">
                        <i class="fas fa-shopping-cart fa-3x" style="margin-bottom: 1rem;"></i>
                        <p>কার্ট খালি</p>
                    </div>
                </div>

                <div class="cart-summary">
                    <div class="summary-row">
                        <span>সাবটোটাল:</span>
                        <span id="subtotal">৳0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>ছাড়:</span>
                        <span id="discount-display">৳0.00</span>
                    </div>
                    <div class="summary-row summary-total">
                        <span>মোট:</span>
                        <span id="total">৳0.00</span>
                    </div>

                    <form method="POST" id="sale-form">
                        <input type="hidden" name="action" value="process_sale">
                        <input type="hidden" name="cart_items" id="cart-items-input">

                        <div class="form-group">
                            <label>কাস্টমার নাম (ঐচ্ছিক)</label>
                            <input type="text" name="customer_name" class="form-control" placeholder="কাস্টমার নাম">
                        </div>

                        <div class="form-group">
                            <label>ফোন নম্বর (ঐচ্ছিক)</label>
                            <input type="tel" name="customer_phone" class="form-control" placeholder="ফোন নম্বর">
                        </div>

                        <div class="form-group">
                            <label>ছাড় (৳)</label>
                            <input type="number" name="discount_amount" id="discount-input" class="form-control"
                                   value="0" min="0" step="0.01" onchange="updateTotal()">
                        </div>

                        <div class="form-group">
                            <label>পেমেন্ট পদ্ধতি</label>
                            <select name="payment_method" class="form-control" required>
                                <option value="cash">নগদ</option>
                                <option value="card">কার্ড</option>
                                <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>নোট (ঐচ্ছিক)</label>
                            <textarea name="notes" class="form-control" rows="2" placeholder="অতিরিক্ত নোট"></textarea>
                        </div>

                        <button type="submit" class="btn btn-success" style="width: 100%;" id="process-sale-btn" disabled>
                            <i class="fas fa-credit-card"></i> বিক্রয় সম্পন্ন করুন
                        </button>

                        <button type="button" class="btn btn-danger" style="width: 100%; margin-top: 0.5rem;" onclick="clearCart()">
                            <i class="fas fa-trash"></i> কার্ট খালি করুন
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/jquery.min.js"></script>
    <script>
        let cart = [];
        let products = <?php echo json_encode($products); ?>;

        function searchProducts() {
            const searchTerm = document.getElementById('product-search').value.toLowerCase();
            const grid = document.getElementById('products-grid');

            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.category_name.toLowerCase().includes(searchTerm)
            );

            let html = '';
            filteredProducts.forEach(product => {
                html += `
                    <div class="product-card" onclick="addToCart(${JSON.stringify(product).replace(/"/g, '&quot;')})">
                        <i class="fas fa-box fa-2x" style="color: #667eea; margin-bottom: 0.5rem;"></i>
                        <h4>${product.name}</h4>
                        <div class="product-price">৳${parseFloat(product.discount_price || product.price).toFixed(2)}</div>
                        <div class="product-stock">স্টক: ${product.stock_quantity}</div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                if (existingItem.quantity < product.stock_quantity) {
                    existingItem.quantity++;
                } else {
                    alert('স্টক সীমা অতিক্রম করা যাবে না!');
                    return;
                }
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: parseFloat(product.discount_price || product.price),
                    quantity: 1,
                    stock: product.stock_quantity
                });
            }

            updateCartDisplay();
        }

        function updateQuantity(productId, newQuantity) {
            const item = cart.find(item => item.id === productId);
            if (item) {
                if (newQuantity <= 0) {
                    removeFromCart(productId);
                } else if (newQuantity <= item.stock) {
                    item.quantity = parseInt(newQuantity);
                    updateCartDisplay();
                } else {
                    alert('স্টক সীমা অতিক্রম করা যাবে না!');
                }
            }
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartItemsDiv = document.getElementById('cart-items');

            if (cart.length === 0) {
                cartItemsDiv.innerHTML = `
                    <div style="text-align: center; color: #666; padding: 2rem;">
                        <i class="fas fa-shopping-cart fa-3x" style="margin-bottom: 1rem;"></i>
                        <p>কার্ট খালি</p>
                    </div>
                `;
                document.getElementById('process-sale-btn').disabled = true;
            } else {
                let html = '';
                cart.forEach(item => {
                    html += `
                        <div class="cart-item">
                            <div class="item-info">
                                <div class="item-name">${item.name}</div>
                                <div class="item-price">৳${item.price.toFixed(2)} × ${item.quantity}</div>
                            </div>
                            <div class="quantity-controls">
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                                <input type="number" class="quantity-input" value="${item.quantity}"
                                       onchange="updateQuantity(${item.id}, this.value)" min="1" max="${item.stock}">
                                <button class="quantity-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                                <button class="remove-btn" onclick="removeFromCart(${item.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                });
                cartItemsDiv.innerHTML = html;
                document.getElementById('process-sale-btn').disabled = false;
            }

            updateTotal();
        }

        function updateTotal() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discount = parseFloat(document.getElementById('discount-input').value) || 0;
            const total = subtotal - discount;

            document.getElementById('subtotal').textContent = `৳${subtotal.toFixed(2)}`;
            document.getElementById('discount-display').textContent = `৳${discount.toFixed(2)}`;
            document.getElementById('total').textContent = `৳${total.toFixed(2)}`;

            // Update hidden input
            document.getElementById('cart-items-input').value = JSON.stringify(cart);
        }

        function clearCart() {
            if (confirm('কার্ট খালি করতে চান?')) {
                cart = [];
                updateCartDisplay();
            }
        }

        // Initialize
        updateCartDisplay();
    </script>
</body>
</html>
