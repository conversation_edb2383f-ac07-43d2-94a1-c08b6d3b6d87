<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

// Handle form submissions
if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_order_status') {
        $order_id = (int)$_POST['order_id'];
        $status = sanitize_input($_POST['status']);
        
        try {
            $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $order_id]);
            
            log_activity('order_status_updated', "Updated order #$order_id status to $status", $_SESSION['admin_id'], 'admin');
            $success = 'অর্ডার স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে।';
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
    
    if ($action === 'update_payment_status') {
        $order_id = (int)$_POST['order_id'];
        $payment_status = sanitize_input($_POST['payment_status']);
        
        try {
            $stmt = $pdo->prepare("UPDATE orders SET payment_status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$payment_status, $order_id]);
            
            log_activity($_SESSION['admin_id'], 'payment_status_updated', "Updated order #$order_id payment status to $payment_status");
            $success = 'পেমেন্ট স্ট্যাটাস সফলভাবে আপডেট করা হয়েছে।';
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? sanitize_input($_GET['status']) : '';
$payment_filter = isset($_GET['payment']) ? sanitize_input($_GET['payment']) : '';
$customer_filter = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Get customer info if filtering by customer
$customer_info = null;
if ($customer_filter) {
    try {
        $stmt = $pdo->prepare("SELECT name, phone, email FROM customers WHERE id = ?");
        $stmt->execute([$customer_filter]);
        $customer_info = $stmt->fetch();
    } catch(PDOException $e) {
        // Ignore error
    }
}
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$sql = "SELECT o.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone 
        FROM orders o 
        LEFT JOIN customers c ON o.customer_id = c.id 
        WHERE 1=1";
$params = [];

if ($status_filter) {
    $sql .= " AND o.status = ?";
    $params[] = $status_filter;
}

if ($payment_filter) {
    $sql .= " AND o.payment_status = ?";
    $params[] = $payment_filter;
}

if ($customer_filter) {
    $sql .= " AND o.customer_id = ?";
    $params[] = $customer_filter;
}

if ($search) {
    $sql .= " AND (o.order_number LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$sql .= " ORDER BY o.created_at DESC LIMIT $limit OFFSET $offset";

// Initialize variables
$orders = [];
$totalOrders = 0;
$totalPages = 0;

try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) FROM orders o LEFT JOIN customers c ON o.customer_id = c.id WHERE 1=1";
    $countParams = [];
    
    if ($status_filter) {
        $countSql .= " AND o.status = ?";
        $countParams[] = $status_filter;
    }
    
    if ($payment_filter) {
        $countSql .= " AND o.payment_status = ?";
        $countParams[] = $payment_filter;
    }

    if ($customer_filter) {
        $countSql .= " AND o.customer_id = ?";
        $countParams[] = $customer_filter;
    }

    if ($search) {
        $countSql .= " AND (o.order_number LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
        $searchTerm = "%$search%";
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
        $countParams[] = $searchTerm;
    }
    
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($countParams);
    $totalOrders = $countStmt->fetchColumn();
    $totalPages = ceil($totalOrders / $limit);
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    $orders = [];
    $totalOrders = 0;
    $totalPages = 0;
}

// Status options
$statusOptions = [
    'pending' => 'অপেক্ষমান',
    'confirmed' => 'নিশ্চিত',
    'processing' => 'প্রক্রিয়াধীন',
    'shipped' => 'পাঠানো হয়েছে',
    'delivered' => 'ডেলিভার হয়েছে',
    'cancelled' => 'বাতিল'
];

$paymentStatusOptions = [
    'pending' => 'অপেক্ষমান',
    'paid' => 'পরিশোধিত',
    'failed' => 'ব্যর্থ',
    'refunded' => 'ফেরত'
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অর্ডার ব্যবস্থাপনা - অ্যাডমিন প্যানেল</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        .sidebar-header {
            text-align: center;
            padding: 0 1rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        .sidebar-menu {
            list-style: none;
        }
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 1.5rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: #ffd700;
        }
        .main-content {
            flex: 1;
            background: #f8f9fa;
        }
        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .admin-body {
            padding: 2rem;
        }
        .filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        .order-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        .order-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.25rem;
        }
        .info-value {
            font-weight: 500;
        }
        .order-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        .status-select {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            text-decoration: none;
            color: #333;
            border-radius: 4px;
        }
        .pagination a:hover {
            background: #667eea;
            color: white;
        }
        .pagination .current {
            background: #667eea;
            color: white;
        }
        @media (max-width: 768px) {
            .admin-layout {
                flex-direction: column;
            }
            .sidebar {
                width: 100%;
            }
            .filters {
                grid-template-columns: 1fr;
            }
            .order-info {
                grid-template-columns: 1fr;
            }
            .order-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-store"></i> অ্যাডমিন প্যানেল</h2>
                <p>স্বাগতম, <?php echo $_SESSION['admin_name']; ?></p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য ব্যবস্থাপনা</a></li>
                <li><a href="categories.php"><i class="fas fa-tags"></i> ক্যাটেগরি</a></li>
                <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i> অর্ডার</a></li>
                <li><a href="customers.php"><i class="fas fa-users"></i> কাস্টমার</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> সেটিংস</a></li>
                <li><a href="ssl_settings.php"><i class="fas fa-credit-card"></i> SSL Commerz</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a></li>
            </ul>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="admin-header">
                <h1>
                    অর্ডার ব্যবস্থাপনা
                    <?php if ($customer_info): ?>
                        - <?php echo htmlspecialchars($customer_info['name']); ?>
                        <small style="font-weight: normal; color: #666;">
                            (<?php echo htmlspecialchars($customer_info['phone']); ?>)
                        </small>
                    <?php endif; ?>
                </h1>
                <div>
                    <span>মোট অর্ডার: <?php echo $totalOrders; ?>টি</span>
                    <?php if ($customer_filter): ?>
                        <a href="orders.php" class="btn btn-secondary" style="margin-left: 1rem;">
                            <i class="fas fa-times"></i> ফিল্টার সরান
                        </a>
                    <?php endif; ?>
                </div>
            </header>
            
            <div class="admin-body">
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Filters -->
                <div class="filters">
                    <div class="form-group">
                        <label>অর্ডার খুঁজুন</label>
                        <input type="text" id="search-input" class="form-control" placeholder="অর্ডার নম্বর, কাস্টমার নাম..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="form-group">
                        <label>অর্ডার স্ট্যাটাস</label>
                        <select id="status-filter" class="form-control">
                            <option value="">সব স্ট্যাটাস</option>
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $status_filter == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>পেমেন্ট স্ট্যাটাস</label>
                        <select id="payment-filter" class="form-control">
                            <option value="">সব পেমেন্ট</option>
                            <?php foreach ($paymentStatusOptions as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php echo $payment_filter == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i> ফিল্টার করুন
                        </button>
                    </div>
                </div>
                
                <!-- Orders List -->
                <?php if (empty($orders)): ?>
                    <div class="order-card">
                        <div style="text-align: center; padding: 2rem;">
                            <i class="fas fa-shopping-cart fa-3x" style="color: #ccc; margin-bottom: 1rem;"></i>
                            <h3>কোন অর্ডার পাওয়া যায়নি</h3>
                            <p>নির্বাচিত ফিল্টার অনুযায়ী কোন অর্ডার খুঁজে পাওয়া যায়নি।</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="order-card">
                            <div class="order-header">
                                <div>
                                    <h4>অর্ডার #<?php echo $order['order_number']; ?></h4>
                                    <small><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></small>
                                </div>
                                <div>
                                    <span class="badge badge-<?php echo $order['status']; ?>">
                                        <?php echo $statusOptions[$order['status']] ?? $order['status']; ?>
                                    </span>
                                    <span class="badge badge-<?php echo $order['payment_status']; ?>">
                                        <?php echo $paymentStatusOptions[$order['payment_status']] ?? $order['payment_status']; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="order-info">
                                <div class="info-item">
                                    <span class="info-label">কাস্টমার</span>
                                    <span class="info-value"><?php echo $order['customer_name']; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">ইমেইল</span>
                                    <span class="info-value"><?php echo $order['customer_email']; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">ফোন</span>
                                    <span class="info-value"><?php echo $order['customer_phone'] ?: 'N/A'; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">মোট পরিমাণ</span>
                                    <span class="info-value"><?php echo format_price($order['total_amount']); ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">পেমেন্ট পদ্ধতি</span>
                                    <span class="info-value"><?php echo ucfirst(str_replace('_', ' ', $order['payment_method'])); ?></span>
                                </div>
                            </div>
                            
                            <?php if ($order['shipping_address']): ?>
                                <div style="margin: 1rem 0;">
                                    <span class="info-label">ডেলিভারি ঠিকানা:</span>
                                    <p style="margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 4px;">
                                        <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="order-actions">
                                <form method="POST" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                                    <input type="hidden" name="action" value="update_order_status">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                    <label>অর্ডার স্ট্যাটাস:</label>
                                    <select name="status" class="status-select" onchange="this.form.submit()">
                                        <?php foreach ($statusOptions as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $order['status'] == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </form>
                                
                                <form method="POST" style="display: inline-flex; gap: 0.5rem; align-items: center;">
                                    <input type="hidden" name="action" value="update_payment_status">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                    <label>পেমেন্ট স্ট্যাটাস:</label>
                                    <select name="payment_status" class="status-select" onchange="this.form.submit()">
                                        <?php foreach ($paymentStatusOptions as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $order['payment_status'] == $value ? 'selected' : ''; ?>><?php echo $label; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </form>
                                
                                <button class="btn btn-secondary btn-sm" onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                    <i class="fas fa-eye"></i> বিস্তারিত
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if ($page > 1): ?>
                            <a href="<?php echo updateUrlParam('page', $page - 1); ?>">&laquo; পূর্ববর্তী</a>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="current"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a href="<?php echo updateUrlParam('page', $i); ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if ($page < $totalPages): ?>
                            <a href="<?php echo updateUrlParam('page', $page + 1); ?>">পরবর্তী &raquo;</a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
    
    <script src="../assets/js/jquery.min.js"></script>
    <script>
        function applyFilters() {
            const search = $('#search-input').val();
            const status = $('#status-filter').val();
            const payment = $('#payment-filter').val();
            
            let url = 'orders.php?';
            const params = [];
            
            if (search) params.push('search=' + encodeURIComponent(search));
            if (status) params.push('status=' + encodeURIComponent(status));
            if (payment) params.push('payment=' + encodeURIComponent(payment));

            // Preserve customer filter
            <?php if ($customer_filter): ?>
            params.push('customer_id=<?php echo $customer_filter; ?>');
            <?php endif; ?>
            
            window.location.href = url + params.join('&');
        }
        
        function viewOrderDetails(orderId) {
            // This would open order details modal or page
            alert('অর্ডার বিস্তারিত দেখার ফিচার শীঘ্রই আসছে!');
        }
        
        // Enter key search
        $('#search-input').on('keypress', function(e) {
            if (e.which === 13) {
                applyFilters();
            }
        });
    </script>
</body>
</html>

<?php
function updateUrlParam($param, $value) {
    $params = $_GET;
    $params[$param] = $value;
    return 'orders.php?' . http_build_query($params);
}
?>
