// Main JavaScript file for the online shop

// Global variables
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Document ready function
$(document).ready(function() {
    updateCartCount();
    
    // Initialize tooltips if using Bootstrap
    if (typeof $().tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }
});

// Load featured products
function loadFeaturedProducts() {
    $.ajax({
        url: 'api/get_products.php',
        method: 'GET',
        data: { featured: true, limit: 6 },
        dataType: 'json',
        beforeSend: function() {
            $('#featured-products').html('<div class="loading"><div class="spinner"></div><p>পণ্য লোড হচ্ছে...</p></div>');
        },
        success: function(response) {
            if (response.success) {
                displayProducts(response.data, '#featured-products');
            } else {
                $('#featured-products').html('<p class="alert alert-error">পণ্য লোড করতে সমস্যা হয়েছে।</p>');
            }
        },
        error: function() {
            $('#featured-products').html('<p class="alert alert-error">সার্ভার এরর। পরে আবার চেষ্টা করুন।</p>');
        }
    });
}

// Load categories
function loadCategories() {
    $.ajax({
        url: 'api/get_categories.php',
        method: 'GET',
        dataType: 'json',
        beforeSend: function() {
            $('#categories-list').html('<div class="loading"><div class="spinner"></div><p>ক্যাটেগরি লোড হচ্ছে...</p></div>');
        },
        success: function(response) {
            if (response.success) {
                displayCategories(response.data, '#categories-list');
            } else {
                $('#categories-list').html('<p class="alert alert-error">ক্যাটেগরি লোড করতে সমস্যা হয়েছে।</p>');
            }
        },
        error: function() {
            $('#categories-list').html('<p class="alert alert-error">সার্ভার এরর। পরে আবার চেষ্টা করুন।</p>');
        }
    });
}

// Display products
function displayProducts(products, container) {
    let html = '';
    
    if (products.length === 0) {
        html = '<p class="alert alert-info">কোন পণ্য পাওয়া যায়নি।</p>';
    } else {
        products.forEach(function(product) {
            const discountPrice = product.discount_price ? parseFloat(product.discount_price) : null;
            const originalPrice = parseFloat(product.price);
            const displayPrice = discountPrice || originalPrice;
            
            html += `
                <div class="product-card">
                    <img src="${product.image ? 'uploads/products/' + product.image : 'assets/images/no-image.svg'}"
                         alt="${product.name}" class="product-image">
                    <div class="product-info">
                        <h4 class="product-name">${product.name}</h4>
                        <div class="product-price">
                            ${formatPrice(displayPrice)}
                            ${discountPrice ? `<span class="original-price">${formatPrice(originalPrice)}</span>` : ''}
                        </div>
                        <p class="product-description">${product.description ? product.description.substring(0, 100) + '...' : ''}</p>
                        <div class="product-actions">
                            <button class="btn btn-primary" onclick="addToCart(${product.id}, '${product.name}', ${displayPrice})">
                                <i class="fas fa-cart-plus"></i> কার্টে যোগ করুন
                            </button>
                            <a href="product_details.php?id=${product.id}" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> বিস্তারিত
                            </a>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    $(container).html(html);
}

// Get category-specific icon
function getCategoryIcon(categoryName) {
    const name = categoryName.toLowerCase();

    // Electronics & Technology
    if (name.includes('ইলেকট্রনিক') || name.includes('মোবাইল') || name.includes('ফোন') || name.includes('ল্যাপটপ') || name.includes('কম্পিউটার')) {
        return { icon: 'fas fa-mobile-alt', color: '#667eea' };
    }

    // Fashion & Clothing
    if (name.includes('পোশাক') || name.includes('জামা') || name.includes('কাপড়') || name.includes('শার্ট') || name.includes('প্যান্ট')) {
        return { icon: 'fas fa-tshirt', color: '#ff6b6b' };
    }

    // Food & Grocery
    if (name.includes('খাবার') || name.includes('খাদ্য') || name.includes('গ্রোসারি') || name.includes('চাল') || name.includes('ডাল')) {
        return { icon: 'fas fa-apple-alt', color: '#2ed573' };
    }

    // Books & Education
    if (name.includes('বই') || name.includes('শিক্ষা') || name.includes('বুক') || name.includes('স্টেশনারি')) {
        return { icon: 'fas fa-book', color: '#ffa502' };
    }

    // Home & Garden
    if (name.includes('ঘর') || name.includes('বাগান') || name.includes('আসবাব') || name.includes('ফার্নিচার')) {
        return { icon: 'fas fa-home', color: '#ff4757' };
    }

    // Sports & Fitness
    if (name.includes('খেলা') || name.includes('স্পোর্টস') || name.includes('ব্যায়াম') || name.includes('ফিটনেস')) {
        return { icon: 'fas fa-futbol', color: '#3742fa' };
    }

    // Beauty & Health
    if (name.includes('সৌন্দর্য') || name.includes('স্বাস্থ্য') || name.includes('কসমেটিক') || name.includes('মেডিসিন')) {
        return { icon: 'fas fa-heart', color: '#ff3838' };
    }

    // Toys & Kids
    if (name.includes('খেলনা') || name.includes('শিশু') || name.includes('বাচ্চা') || name.includes('টয়')) {
        return { icon: 'fas fa-child', color: '#ff9ff3' };
    }

    // Automotive
    if (name.includes('গাড়ি') || name.includes('মোটর') || name.includes('বাইক') || name.includes('যন্ত্রাংশ')) {
        return { icon: 'fas fa-car', color: '#2f3542' };
    }

    // Default icon
    return { icon: 'fas fa-tags', color: '#667eea' };
}

// Display categories
function displayCategories(categories, container) {
    let html = '';
    
    if (categories.length === 0) {
        html = '<p class="alert alert-info">কোন ক্যাটেগরি পাওয়া যায়নি।</p>';
    } else {
        categories.forEach(function(category) {
            // Get category-specific icon
            const categoryIcon = getCategoryIcon(category.name);

            // Check if category has new products (added in last 7 days)
            const hasNewProducts = category.new_products_count > 0;
            const newBadge = hasNewProducts ? `
                <div class="new-badge">
                    <span class="new-text">নতুন ${category.new_products_count}টি</span>
                    <span class="new-subtext">পণ্য যোগ হয়েছে</span>
                </div>
            ` : '';

            html += `
                <div class="category-card ${hasNewProducts ? 'has-new' : ''}">
                    ${newBadge}
                    <a href="products.php?category=${category.id}">
                        <i class="${categoryIcon.icon} fa-2x" style="color: ${categoryIcon.color}; margin-bottom: 1rem;"></i>
                        <h4>${category.name}</h4>
                        <p>${category.description || ''}</p>
                    </a>
                </div>
            `;
        });
    }
    
    $(container).html(html);
}

// Add to cart function
function addToCart(productId, productName, price) {
    // Check if product already exists in cart
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: price,
            quantity: 1
        });
    }
    
    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart count
    updateCartCount();
    
    // Show success message
    showAlert('পণ্যটি কার্টে যোগ করা হয়েছে!', 'success');
    
    // If user is logged in, also save to database
    saveCartToDatabase();
}

// Remove from cart
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCartCount();
    
    // Update database if user is logged in
    saveCartToDatabase();
}

// Update cart count
function updateCartCount() {
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    $('#cart-count').text(totalItems);
}

// Save cart to database (for logged in users)
function saveCartToDatabase() {
    // Check if user is logged in (you can modify this check based on your session handling)
    if (typeof isLoggedIn !== 'undefined' && isLoggedIn) {
        $.ajax({
            url: 'api/save_cart.php',
            method: 'POST',
            data: { cart: JSON.stringify(cart) },
            dataType: 'json',
            success: function(response) {
                // Cart saved successfully
            },
            error: function() {
                // Handle error silently
            }
        });
    }
}

// Format price
function formatPrice(price) {
    return parseFloat(price).toLocaleString('bn-BD') + ' টাকা';
}

// Show alert messages
function showAlert(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-error' : 'alert-info';
    
    const alertHtml = `<div class="alert ${alertClass}">${message}</div>`;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert at the top of main content
    $('.main-content').prepend(alertHtml);
    
    // Auto remove after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut(500, function() {
            $(this).remove();
        });
    }, 3000);
}

// Search functionality
function searchProducts(query) {
    if (query.length < 2) {
        return;
    }
    
    $.ajax({
        url: 'api/search_products.php',
        method: 'GET',
        data: { q: query },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                displaySearchResults(response.data);
            }
        },
        error: function() {
            console.log('Search error');
        }
    });
}

// Display search results
function displaySearchResults(products) {
    const resultsContainer = $('#search-results');
    if (resultsContainer.length) {
        displayProducts(products, '#search-results');
    }
}

// Form validation
function validateForm(formId) {
    let isValid = true;
    const form = $(formId);
    
    // Remove previous error messages
    form.find('.error-message').remove();
    
    // Check required fields
    form.find('[required]').each(function() {
        const field = $(this);
        const value = field.val().trim();
        
        if (!value) {
            isValid = false;
            field.after('<span class="error-message" style="color: red; font-size: 0.9rem;">এই ফিল্ডটি আবশ্যক।</span>');
        }
    });
    
    // Email validation
    form.find('input[type="email"]').each(function() {
        const email = $(this).val().trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            isValid = false;
            $(this).after('<span class="error-message" style="color: red; font-size: 0.9rem;">সঠিক ইমেইল ঠিকানা দিন।</span>');
        }
    });
    
    return isValid;
}

// Initialize search functionality
$(document).ready(function() {
    // Search input handler
    $('#search-input').on('input', function() {
        const query = $(this).val();
        if (query.length >= 2) {
            searchProducts(query);
        }
    });
    
    // Form submission handlers
    $('form').on('submit', function(e) {
        const formId = '#' + $(this).attr('id');
        if (!validateForm(formId)) {
            e.preventDefault();
        }
    });
});
