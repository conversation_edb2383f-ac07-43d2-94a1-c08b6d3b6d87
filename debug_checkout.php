<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';

echo "<h2>Checkout Debug Information</h2>";

// Check if user is logged in
echo "<h3>Login Status:</h3>";
if (isset($_SESSION['customer_id'])) {
    echo "<p style='color: green;'>✅ Logged in as: " . $_SESSION['customer_name'] . " (ID: " . $_SESSION['customer_id'] . ")</p>";
} else {
    echo "<p style='color: red;'>❌ Not logged in</p>";
    echo "<p><a href='login.php'>Login করুন</a></p>";
    exit;
}

// Check session cart
echo "<h3>Session Cart:</h3>";
if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    echo "<p style='color: green;'>✅ Session cart found</p>";
    echo "<pre>" . print_r($_SESSION['cart'], true) . "</pre>";
} else {
    echo "<p style='color: red;'>❌ Session cart empty</p>";
}

// Check localStorage cart (JavaScript)
echo "<h3>LocalStorage Cart:</h3>";
echo "<div id='localStorage-cart'></div>";

// Check database cart
echo "<h3>Database Cart:</h3>";
try {
    $stmt = $pdo->prepare("SELECT c.*, p.name, p.price, p.discount_price FROM cart c JOIN products p ON c.product_id = p.id WHERE c.customer_id = ?");
    $stmt->execute([$_SESSION['customer_id']]);
    $db_cart = $stmt->fetchAll();
    
    if ($db_cart) {
        echo "<p style='color: green;'>✅ Database cart found</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Product</th><th>Price</th><th>Quantity</th><th>Added</th></tr>";
        foreach ($db_cart as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>৳" . number_format($item['discount_price'] ?: $item['price'], 2) . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>" . $item['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ Database cart empty</p>";
    }
} catch(PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}

// Test actions
echo "<h3>Test Actions:</h3>";
echo "<button onclick='loadCartFromDB()' style='background: blue; color: white; padding: 10px; margin: 5px;'>Load Cart from Database</button>";
echo "<button onclick='syncLocalStorageCart()' style='background: green; color: white; padding: 10px; margin: 5px;'>Sync LocalStorage Cart</button>";
echo "<button onclick='addTestProduct()' style='background: orange; color: white; padding: 10px; margin: 5px;'>Add Test Product</button>";

echo "<h3>Navigation:</h3>";
echo "<p><a href='index.php'>← Home</a> | <a href='cart.php'>Cart</a> | <a href='checkout.php'>Checkout</a></p>";
?>

<script src="assets/js/jquery.min.js"></script>
<script>
$(document).ready(function() {
    // Check localStorage cart
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    if (cart.length > 0) {
        $('#localStorage-cart').html('<p style="color: green;">✅ LocalStorage cart found (' + cart.length + ' items)</p><pre>' + JSON.stringify(cart, null, 2) + '</pre>');
    } else {
        $('#localStorage-cart').html('<p style="color: red;">❌ LocalStorage cart empty</p>');
    }
});

function loadCartFromDB() {
    $.ajax({
        url: 'api/load_cart.php',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                alert('Cart loaded from database!');
                location.reload();
            } else {
                alert('Failed to load cart: ' + response.message);
            }
        },
        error: function() {
            alert('Error loading cart from database');
        }
    });
}

function syncLocalStorageCart() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    if (cart.length === 0) {
        alert('LocalStorage cart is empty!');
        return;
    }
    
    $.ajax({
        url: 'api/sync_cart.php',
        method: 'POST',
        data: { cart: JSON.stringify(cart) },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                alert('Cart synced successfully!');
                location.reload();
            } else {
                alert('Failed to sync cart: ' + response.message);
            }
        },
        error: function() {
            alert('Error syncing cart');
        }
    });
}

function addTestProduct() {
    // Add test product to localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Add a test product
    const testProduct = {
        id: 1,
        name: 'Test Product',
        price: 100,
        quantity: 2
    };
    
    // Check if product already exists
    const existingIndex = cart.findIndex(item => item.id === testProduct.id);
    if (existingIndex >= 0) {
        cart[existingIndex].quantity += testProduct.quantity;
    } else {
        cart.push(testProduct);
    }
    
    localStorage.setItem('cart', JSON.stringify(cart));
    alert('Test product added to localStorage!');
    location.reload();
}
</script>
