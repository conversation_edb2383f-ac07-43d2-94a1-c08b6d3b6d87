<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];

// Get today's statistics
try {
    // Today's sales
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_sales, COALESCE(SUM(final_amount), 0) as total_amount 
                          FROM pos_sales WHERE salesman_id = ? AND DATE(created_at) = CURDATE()");
    $stmt->execute([$salesman_id]);
    $today_stats = $stmt->fetch();
    
    // This month's statistics
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_sales, COALESCE(SUM(final_amount), 0) as total_amount 
                          FROM pos_sales WHERE salesman_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())");
    $stmt->execute([$salesman_id]);
    $month_stats = $stmt->fetch();
    
    // Recent sales
    $stmt = $pdo->prepare("SELECT * FROM pos_sales WHERE salesman_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$salesman_id]);
    $recent_sales = $stmt->fetchAll();
    
    // Get salesman info
    $stmt = $pdo->prepare("SELECT * FROM salesman WHERE id = ?");
    $stmt->execute([$salesman_id]);
    $salesman = $stmt->fetch();
    
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সেলসম্যান ড্যাশবোর্ড - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .main-content {
            padding: 2rem 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #333;
        }
        .stat-card p {
            color: #666;
            font-size: 0.9rem;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .action-btn {
            display: block;
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-decoration: none;
            color: #333;
            text-align: center;
            transition: transform 0.2s ease;
        }
        .action-btn:hover {
            transform: translateY(-2px);
        }
        .action-btn i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        .recent-sales {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .recent-sales h3 {
            padding: 1rem;
            background: #f8f9fa;
            margin: 0;
            border-bottom: 1px solid #e9ecef;
        }
        .sales-table {
            width: 100%;
            border-collapse: collapse;
        }
        .sales-table th,
        .sales-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .sales-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .badge-success {
            background: #d4edda;
            color: #155724;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-menu ul {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <span><i class="fas fa-id-badge"></i> <?php echo $_SESSION['employee_id']; ?></span>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
                <li><a href="change_password.php"><i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-calendar-day" style="color: #667eea;"></i>
                    <h3><?php echo $today_stats['total_sales']; ?></h3>
                    <p>আজকের বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-money-bill-wave" style="color: #2ed573;"></i>
                    <h3>৳<?php echo number_format($today_stats['total_amount'], 2); ?></h3>
                    <p>আজকের আয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-calendar-alt" style="color: #ffa502;"></i>
                    <h3><?php echo $month_stats['total_sales']; ?></h3>
                    <p>এই মাসের বিক্রয়</p>
                </div>
                <div class="stat-card">
                    <i class="fas fa-chart-line" style="color: #ff4757;"></i>
                    <h3>৳<?php echo number_format($month_stats['total_amount'], 2); ?></h3>
                    <p>এই মাসের আয়</p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="pos.php" class="action-btn">
                    <i class="fas fa-plus-circle" style="color: #2ed573;"></i>
                    <strong>নতুন বিক্রয়</strong>
                    <p>পণ্য বিক্রয় করুন</p>
                </a>
                <a href="products.php" class="action-btn">
                    <i class="fas fa-search" style="color: #667eea;"></i>
                    <strong>পণ্য খুঁজুন</strong>
                    <p>পণ্য তালিকা দেখুন</p>
                </a>
                <a href="sales.php" class="action-btn">
                    <i class="fas fa-history" style="color: #ffa502;"></i>
                    <strong>বিক্রয় ইতিহাস</strong>
                    <p>পূর্বের বিক্রয় দেখুন</p>
                </a>
                <a href="reports.php" class="action-btn">
                    <i class="fas fa-chart-bar" style="color: #ff4757;"></i>
                    <strong>রিপোর্ট</strong>
                    <p>বিক্রয় রিপোর্ট দেখুন</p>
                </a>
                <a href="change_password.php" class="action-btn">
                    <i class="fas fa-key" style="color: #9c88ff;"></i>
                    <strong>পাসওয়ার্ড পরিবর্তন</strong>
                    <p>নিরাপত্তা সেটিংস</p>
                </a>
            </div>

            <!-- Recent Sales -->
            <div class="recent-sales">
                <h3><i class="fas fa-clock"></i> সাম্প্রতিক বিক্রয়</h3>
                <?php if (empty($recent_sales)): ?>
                    <div style="padding: 2rem; text-align: center; color: #666;">
                        <i class="fas fa-receipt fa-3x" style="margin-bottom: 1rem;"></i>
                        <p>কোন বিক্রয় পাওয়া যায়নি।</p>
                    </div>
                <?php else: ?>
                    <table class="sales-table">
                        <thead>
                            <tr>
                                <th>বিক্রয় নং</th>
                                <th>কাস্টমার</th>
                                <th>মোট</th>
                                <th>পেমেন্ট</th>
                                <th>সময়</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_sales as $sale): ?>
                            <tr>
                                <td><a href="sale_details.php?id=<?php echo $sale['id']; ?>" style="color: #667eea; text-decoration: none;"><?php echo $sale['sale_number']; ?></a></td>
                                <td><?php echo $sale['customer_name'] ?: 'ওয়াক-ইন কাস্টমার'; ?></td>
                                <td>৳<?php echo number_format($sale['final_amount'], 2); ?></td>
                                <td>
                                    <span class="badge badge-success">
                                        <?php
                                        $payment_methods = [
                                            'cash' => 'নগদ',
                                            'card' => 'কার্ড',
                                            'mobile_banking' => 'মোবাইল ব্যাংকিং'
                                        ];
                                        echo $payment_methods[$sale['payment_method']] ?? $sale['payment_method'];
                                        ?>
                                    </span>
                                </td>
                                <td><?php echo date('d/m/Y H:i', strtotime($sale['created_at'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
