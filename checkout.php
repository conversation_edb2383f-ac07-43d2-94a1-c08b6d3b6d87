<?php
require_once 'config/session.php';
require_once 'config/database.php';
require_once 'config/functions.php';
require_once 'config/sslcommerz.php';

// Check if user is logged in
if (!isset($_SESSION['customer_id'])) {
    redirect('login.php');
}

$customer_id = $_SESSION['customer_id'];
$message = '';

// Get customer info
try {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
} catch(PDOException $e) {
    $message = '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">Database error: ' . $e->getMessage() . '</div>';
}

// Get cart items
$cart_items = [];
$total_amount = 0;

// Debug: Show cart session
$debug_info = '';
if (isset($_SESSION['cart'])) {
    $debug_info .= '<div style="background: #e7f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <strong>Debug - Session Cart:</strong> ' . print_r($_SESSION['cart'], true) . '
    </div>';
} else {
    $debug_info .= '<div style="background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px;">
        <strong>Debug:</strong> No session cart found
    </div>';
}

if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
    try {
        $product_ids = array_keys($_SESSION['cart']);
        $debug_info .= '<div style="background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <strong>Debug - Product IDs:</strong> ' . implode(', ', $product_ids) . '
        </div>';

        $placeholders = str_repeat('?,', count($product_ids) - 1) . '?';
        $sql = "SELECT * FROM products WHERE id IN ($placeholders) AND status = 'active'";
        $debug_info .= '<div style="background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <strong>Debug - SQL:</strong> ' . $sql . '<br>
            <strong>Parameters:</strong> ' . implode(', ', $product_ids) . '
        </div>';

        $stmt = $pdo->prepare($sql);
        $stmt->execute($product_ids);
        $products = $stmt->fetchAll();

        $debug_info .= '<div style="background: #e2e3e5; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <strong>Debug - Products Found:</strong> ' . count($products) . ' products<br>';

        if ($products) {
            foreach ($products as $product) {
                $debug_info .= 'Product ID: ' . $product['id'] . ', Name: ' . $product['name'] . ', Status: ' . $product['status'] . '<br>';
            }
        } else {
            $debug_info .= 'No products found in database';
        }
        $debug_info .= '</div>';

        foreach ($products as $product) {
            $quantity = $_SESSION['cart'][$product['id']];
            $price = $product['discount_price'] ?: $product['price'];
            $subtotal = $price * $quantity;
            $total_amount += $subtotal;

            $cart_items[] = [
                'product' => $product,
                'quantity' => $quantity,
                'price' => $price,
                'subtotal' => $subtotal
            ];
        }

        // Debug cart items
        $debug_info .= '<div style="background: #d1ecf1; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <strong>Debug - Cart Items Count:</strong> ' . count($cart_items) . '<br>
            <strong>Debug - Total Amount:</strong> ৳' . number_format($total_amount, 2) . '<br>';

        if (!empty($cart_items)) {
            $debug_info .= '<strong>Debug - Cart Items:</strong><br>';
            foreach ($cart_items as $index => $item) {
                $debug_info .= 'Item ' . ($index + 1) . ': ' . $item['product']['name'] . ' (Qty: ' . $item['quantity'] . ', Price: ৳' . $item['price'] . ')<br>';
            }
        }
        $debug_info .= '</div>';
    } catch(PDOException $e) {
        $message = '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">Error loading cart: ' . $e->getMessage() . '</div>';
        $debug_info .= '<div style="background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <strong>Debug - Database Error:</strong> ' . $e->getMessage() . '
        </div>';
    }
}

// Debug POST request
$debug_info .= '<div style="background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 5px;">
    <strong>Debug - POST Check:</strong><br>
    POST Request: ' . ($_POST ? 'YES' : 'NO') . '<br>
    POST Action: ' . ($_POST['action'] ?? 'None') . '<br>
    Will Process Order: ' . (($_POST && $_POST['action'] === 'place_order') ? 'YES' : 'NO') . '
</div>';

// Handle order submission
if ($_POST && $_POST['action'] === 'place_order') {
    if (empty($cart_items)) {
        $message = '<div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 1rem; margin: 1rem 0; border-radius: 5px;">আপনার কার্ট খালি!</div>';
    } else {
        $delivery_address = sanitize_input($_POST['delivery_address'] ?? '');
        $notes = sanitize_input($_POST['notes'] ?? '');
        $payment_method = sanitize_input($_POST['payment_method'] ?? 'cod');

        if (empty($delivery_address)) {
            $message = '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">ডেলিভারি ঠিকানা আবশ্যক।</div>';
        } else {
            try {
                // Start transaction
                $pdo->beginTransaction();

                // Generate order number and transaction ID
                $order_number = 'ORD' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $transaction_id = 'TXN' . date('YmdHis') . rand(1000, 9999);

                // Create order
                $stmt = $pdo->prepare("INSERT INTO orders (customer_id, order_number, transaction_id, total_amount, shipping_address, notes, payment_method, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())");
                $stmt->execute([$customer_id, $order_number, $transaction_id, $total_amount, $delivery_address, $notes, $payment_method]);
                $order_id = $pdo->lastInsertId();

                // Add order items
                foreach ($cart_items as $item) {
                    $total_item = $item['price'] * $item['quantity'];
                    $stmt = $pdo->prepare("INSERT INTO order_items (order_id, product_id, quantity, price, total) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$order_id, $item['product']['id'], $item['quantity'], $item['price'], $total_item]);
                }

                // Commit transaction
                $pdo->commit();

                // Log activity
                log_activity('Order Placed', "Order #$order_id placed for " . format_price($total_amount), $customer_id, 'customer');

                if ($payment_method === 'sslcommerz') {
                    // Prepare SSL Commerz payment data
                    $order_data = [
                        'customer_name' => $customer['name'],
                        'customer_email' => $customer['email'],
                        'customer_phone' => $customer['phone'],
                        'customer_address' => $delivery_address,
                        'total_amount' => $total_amount,
                        'transaction_id' => $transaction_id,
                        'product_name' => count($cart_items) > 1 ? 'Multiple Products' : $cart_items[0]['product']['name']
                    ];

                    // Initiate SSL Commerz payment
                    $payment_url = SSLCommerz::initiate_payment($order_data);

                    if ($payment_url) {
                        // Clear cart
                        unset($_SESSION['cart']);

                        // Redirect to SSL Commerz
                        header("Location: $payment_url");
                        exit;
                    } else {
                        $message = '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">পেমেন্ট গেটওয়ে শুরু করতে সমস্যা হয়েছে। পরে আবার চেষ্টা করুন।</div>';
                    }
                } else {
                    // Cash on Delivery
                    // Clear cart
                    unset($_SESSION['cart']);

                    $message = '<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; margin: 1rem 0; border-radius: 5px;"><i class="fas fa-check-circle"></i> অর্ডার সফলভাবে সম্পন্ন হয়েছে! অর্ডার নম্বর: #' . $order_number . '<br><a href="orders.php">আপনার অর্ডার দেখুন</a></div>';

                    // Clear cart items for display
                    $cart_items = [];
                    $total_amount = 0;
                }

            } catch(PDOException $e) {
                $pdo->rollBack();
                $message = '<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; margin: 1rem 0; border-radius: 5px;">অর্ডার প্রক্রিয়াকরণে সমস্যা: ' . $e->getMessage() . '</div>';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>চেকআউট - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .checkout-container {
            max-width: 1000px;
            margin: 2rem auto;
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
        }
        .checkout-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .order-summary {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .cart-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #eee;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .item-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
            margin-right: 1rem;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .item-price {
            color: #666;
            font-size: 0.9rem;
        }
        .total-section {
            border-top: 2px solid #eee;
            padding-top: 1rem;
            margin-top: 1rem;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .total-final {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c5aa0;
        }
        @media (max-width: 768px) {
            .checkout-container {
                grid-template-columns: 1fr;
                margin: 1rem;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="index.php">
                    <i class="fas fa-store"></i>
                    <?php echo SITE_NAME; ?>
                </a>
            </div>
            <nav class="nav-menu">
                <a href="index.php"><i class="fas fa-home"></i> হোম</a>
                <a href="cart.php"><i class="fas fa-shopping-cart"></i> কার্ট</a>
                <a href="orders.php"><i class="fas fa-list"></i> অর্ডার</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt"></i> লগআউট</a>
            </nav>
        </div>
    </header>

    <main class="main-content">
        <div class="checkout-container">
            <!-- Checkout Form -->
            <div class="checkout-form">
                <h2><i class="fas fa-credit-card"></i> চেকআউট</h2>
                
                <?php if ($message): ?>
                    <?php echo $message; ?>
                <?php endif; ?>

                <?php if (isset($debug_info)): ?>
                    <?php echo $debug_info; ?>
                <?php endif; ?>
                
                <?php if (!empty($cart_items)): ?>
                <form method="POST">
                    <input type="hidden" name="action" value="place_order">
                    
                    <div class="form-group">
                        <label for="delivery_address">ডেলিভারি ঠিকানা *</label>
                        <textarea name="delivery_address" id="delivery_address" class="form-control" rows="3" required><?php echo htmlspecialchars($customer['address'] ?? ''); ?></textarea>
                        <small>আপনার ফোন নম্বর: <?php echo htmlspecialchars($customer['phone'] ?? ''); ?></small>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">অতিরিক্ত নোট (ঐচ্ছিক)</label>
                        <textarea name="notes" id="notes" class="form-control" rows="2" placeholder="বিশেষ নির্দেশনা..."></textarea>
                    </div>

                    <div class="form-group">
                        <label>পেমেন্ট পদ্ধতি *</label>
                        <div style="margin-top: 0.5rem;">
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: normal;">
                                <input type="radio" name="payment_method" value="cod" checked style="margin-right: 0.5rem;">
                                <i class="fas fa-money-bill-wave"></i> ক্যাশ অন ডেলিভারি
                            </label>
                            <?php if (defined('SSLC_IS_ENABLED') && SSLC_IS_ENABLED): ?>
                            <label style="display: block; margin-bottom: 0.5rem; font-weight: normal;">
                                <input type="radio" name="payment_method" value="sslcommerz" style="margin-right: 0.5rem;">
                                <i class="fas fa-credit-card"></i> অনলাইন পেমেন্ট (SSL Commerz)
                                <small style="display: block; color: #666; margin-left: 1.5rem;">
                                    বিকাশ, নগদ, রকেট, ভিসা, মাস্টারকার্ড
                                </small>
                            </label>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            <i class="fas fa-check"></i> অর্ডার নিশ্চিত করুন
                        </button>
                    </div>
                </form>
                <?php endif; ?>
            </div>
            
            <!-- Order Summary -->
            <div class="order-summary">
                <h3><i class="fas fa-list"></i> অর্ডার সারসংক্ষেপ</h3>
                
                <!-- Debug: Check cart_items at display time -->
                <div style="background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 5px;">
                    <strong>Debug - Display Check:</strong><br>
                    Cart Items Count: <?php echo count($cart_items); ?><br>
                    Empty Check: <?php echo empty($cart_items) ? 'TRUE (empty)' : 'FALSE (not empty)'; ?><br>
                    Is Array: <?php echo is_array($cart_items) ? 'YES' : 'NO'; ?>
                </div>

                <?php if (empty($cart_items)): ?>
                    <p style="text-align: center; color: #666; margin: 2rem 0;">
                        <i class="fas fa-shopping-cart" style="font-size: 3rem; margin-bottom: 1rem; display: block;"></i>
                        আপনার কার্ট খালি
                    </p>
                    <button onclick="syncCart()" class="btn btn-secondary" style="width: 100%; margin-bottom: 1rem;">
                        <i class="fas fa-sync"></i> কার্ট সিঙ্ক করুন
                    </button>
                    <a href="index.php" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-shopping-bag"></i> কেনাকাটা শুরু করুন
                    </a>
                <?php else: ?>
                    <?php foreach ($cart_items as $item): ?>
                    <div class="cart-item">
                        <img src="<?php echo $item['product']['image'] ? 'uploads/products/' . $item['product']['image'] : 'assets/images/no-image.jpg'; ?>" 
                             alt="<?php echo htmlspecialchars($item['product']['name']); ?>" class="item-image">
                        <div class="item-details">
                            <div class="item-name"><?php echo htmlspecialchars($item['product']['name']); ?></div>
                            <div class="item-price">
                                <?php echo format_price($item['price']); ?> × <?php echo $item['quantity']; ?> = 
                                <strong><?php echo format_price($item['subtotal']); ?></strong>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="total-section">
                        <div class="total-row">
                            <span>সাবটোটাল:</span>
                            <span><?php echo format_price($total_amount); ?></span>
                        </div>
                        <div class="total-row">
                            <span>ডেলিভারি চার্জ:</span>
                            <span>ফ্রি</span>
                        </div>
                        <div class="total-row total-final">
                            <span>মোট:</span>
                            <span><?php echo format_price($total_amount); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 <?php echo SITE_NAME; ?>. সকল অধিকার সংরক্ষিত।</p>
        </div>
    </footer>

    <script src="assets/js/jquery.min.js"></script>
    <script>
        // Simple cart count update
        $(document).ready(function() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            $('#cart-count').text(totalItems);
        });

        function syncCart() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];

            if (cart.length === 0) {
                alert('আপনার কার্টে কোন পণ্য নেই!');
                return;
            }

            // Show loading
            $('button[onclick="syncCart()"]').html('<i class="fas fa-spinner fa-spin"></i> সিঙ্ক হচ্ছে...');

            $.ajax({
                url: 'api/sync_cart.php',
                method: 'POST',
                data: { cart: JSON.stringify(cart) },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('কার্ট সিঙ্ক করতে সমস্যা হয়েছে: ' + response.message);
                        $('button[onclick="syncCart()"]').html('<i class="fas fa-sync"></i> কার্ট সিঙ্ক করুন');
                    }
                },
                error: function() {
                    alert('সার্ভার এরর! পরে আবার চেষ্টা করুন।');
                    $('button[onclick="syncCart()"]').html('<i class="fas fa-sync"></i> কার্ট সিঙ্ক করুন');
                }
            });
        }
    </script>
</body>
</html>
