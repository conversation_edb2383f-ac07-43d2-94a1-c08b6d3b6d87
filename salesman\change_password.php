<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if salesman is logged in
if (!isset($_SESSION['salesman_id'])) {
    redirect('login.php');
}

$salesman_id = $_SESSION['salesman_id'];
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = 'সকল ফিল্ড পূরণ করুন';
    } elseif (strlen($new_password) < 6) {
        $error = 'নতুন পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে';
    } elseif ($new_password !== $confirm_password) {
        $error = 'নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ পাসওয়ার্ড মিলছে না';
    } else {
        try {
            // Get current password from database
            $stmt = $pdo->prepare("SELECT password FROM salesman WHERE id = ?");
            $stmt->execute([$salesman_id]);
            $salesman = $stmt->fetch();
            
            if (!$salesman || !password_verify($current_password, $salesman['password'])) {
                $error = 'বর্তমান পাসওয়ার্ড ভুল';
            } else {
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE salesman SET password = ?, updated_at = NOW() WHERE id = ?");
                
                if ($stmt->execute([$hashed_password, $salesman_id])) {
                    $success = 'পাসওয়ার্ড সফলভাবে পরিবর্তন হয়েছে';
                } else {
                    $error = 'পাসওয়ার্ড পরিবর্তনে সমস্যা হয়েছে';
                }
            }
        } catch(PDOException $e) {
            $error = 'ডাটাবেস এরর: ' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পাসওয়ার্ড পরিবর্তন - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-menu {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .nav-menu ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-menu a:hover, .nav-menu a.active {
            background: #667eea;
            color: white;
        }
        .main-content {
            padding: 2rem 0;
        }
        .password-form-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .form-header h2 {
            margin-bottom: 0.5rem;
        }
        .form-header p {
            opacity: 0.9;
        }
        .form-body {
            padding: 2rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
            width: 100%;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
            margin-top: 1rem;
            width: 100%;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .password-requirements {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
        .password-requirements h4 {
            color: #0066cc;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            color: #0066cc;
        }
        .password-requirements li {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
        }
        .back-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-menu ul {
                flex-wrap: wrap;
                gap: 1rem;
            }
            .password-form-container {
                margin: 0 1rem;
            }
            .form-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-cash-register"></i> POS সিস্টেম</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['salesman_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <nav class="nav-menu">
        <div class="container">
            <ul>
                <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড</a></li>
                <li><a href="pos.php"><i class="fas fa-cash-register"></i> নতুন বিক্রয়</a></li>
                <li><a href="sales.php"><i class="fas fa-list"></i> বিক্রয় তালিকা</a></li>
                <li><a href="products.php"><i class="fas fa-box"></i> পণ্য তালিকা</a></li>
                <li><a href="reports.php"><i class="fas fa-chart-bar"></i> রিপোর্ট</a></li>
                <li><a href="change_password.php" class="active"><i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন</a></li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="password-form-container">
                <div class="form-header">
                    <h2><i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন</h2>
                    <p>আপনার অ্যাকাউন্টের নিরাপত্তার জন্য পাসওয়ার্ড পরিবর্তন করুন</p>
                </div>
                
                <div class="form-body">
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <div class="password-requirements">
                        <h4><i class="fas fa-info-circle"></i> পাসওয়ার্ড নির্দেশনা:</h4>
                        <ul>
                            <li>কমপক্ষে ৬ অক্ষরের হতে হবে</li>
                            <li>বড় ও ছোট হাতের অক্ষর ব্যবহার করুন</li>
                            <li>সংখ্যা ও বিশেষ চিহ্ন ব্যবহার করুন</li>
                            <li>সহজে অনুমানযোগ্য পাসওয়ার্ড এড়িয়ে চলুন</li>
                        </ul>
                    </div>

                    <form method="POST">
                        <div class="form-group">
                            <label for="current_password">
                                <i class="fas fa-lock"></i> বর্তমান পাসওয়ার্ড
                            </label>
                            <input type="password" id="current_password" name="current_password" 
                                   class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="new_password">
                                <i class="fas fa-key"></i> নতুন পাসওয়ার্ড
                            </label>
                            <input type="password" id="new_password" name="new_password" 
                                   class="form-control" required minlength="6">
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">
                                <i class="fas fa-check-double"></i> নতুন পাসওয়ার্ড নিশ্চিত করুন
                            </label>
                            <input type="password" id="confirm_password" name="confirm_password" 
                                   class="form-control" required minlength="6">
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> পাসওয়ার্ড পরিবর্তন করুন
                        </button>
                        
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </form>

                    <div class="back-link">
                        <a href="dashboard.php">
                            <i class="fas fa-arrow-left"></i> ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('পাসওয়ার্ড মিলছে না');
            } else {
                this.setCustomValidity('');
            }
        });

        // Password strength indicator
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const strength = getPasswordStrength(password);
            
            // You can add visual feedback here
        });

        function getPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            return strength;
        }
    </script>
</body>
</html>
