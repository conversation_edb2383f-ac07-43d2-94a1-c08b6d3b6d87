<?php
require_once '../config/session.php';
require_once '../config/database.php';
require_once '../config/functions.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    redirect('login.php');
}

$salesman_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$success = '';
$error = '';

if (!$salesman_id) {
    redirect('salesman.php');
}

// Get salesman details
try {
    $stmt = $pdo->prepare("SELECT * FROM salesman WHERE id = ?");
    $stmt->execute([$salesman_id]);
    $salesman = $stmt->fetch();
    
    if (!$salesman) {
        redirect('salesman.php');
    }
} catch(PDOException $e) {
    $error = 'ডাটাবেস এরর: ' . $e->getMessage();
    redirect('salesman.php');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_info') {
        // Update basic info
        $name = sanitize_input($_POST['name']);
        $email = sanitize_input($_POST['email']);
        $phone = sanitize_input($_POST['phone']);
        $employee_id = sanitize_input($_POST['employee_id']);
        $commission_rate = (float)$_POST['commission_rate'];
        $status = sanitize_input($_POST['status']);
        
        if (empty($name) || empty($email) || empty($employee_id)) {
            $error = 'সকল প্রয়োজনীয় ফিল্ড পূরণ করুন';
        } else {
            try {
                $stmt = $pdo->prepare("UPDATE salesman SET name = ?, email = ?, phone = ?, employee_id = ?, commission_rate = ?, status = ?, updated_at = NOW() WHERE id = ?");
                
                if ($stmt->execute([$name, $email, $phone, $employee_id, $commission_rate, $status, $salesman_id])) {
                    $success = 'সেলসম্যানের তথ্য সফলভাবে আপডেট হয়েছে';
                    // Refresh data
                    $stmt = $pdo->prepare("SELECT * FROM salesman WHERE id = ?");
                    $stmt->execute([$salesman_id]);
                    $salesman = $stmt->fetch();
                } else {
                    $error = 'তথ্য আপডেটে সমস্যা হয়েছে';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    } elseif ($action == 'change_password') {
        // Change password
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($new_password) || empty($confirm_password)) {
            $error = 'নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ পাসওয়ার্ড দিন';
        } elseif (strlen($new_password) < 6) {
            $error = 'পাসওয়ার্ড কমপক্ষে ৬ অক্ষরের হতে হবে';
        } elseif ($new_password !== $confirm_password) {
            $error = 'নতুন পাসওয়ার্ড এবং নিশ্চিতকরণ পাসওয়ার্ড মিলছে না';
        } else {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE salesman SET password = ?, updated_at = NOW() WHERE id = ?");
                
                if ($stmt->execute([$hashed_password, $salesman_id])) {
                    $success = 'সেলসম্যানের পাসওয়ার্ড সফলভাবে পরিবর্তন হয়েছে';
                } else {
                    $error = 'পাসওয়ার্ড পরিবর্তনে সমস্যা হয়েছে';
                }
            } catch(PDOException $e) {
                $error = 'ডাটাবেস এরর: ' . $e->getMessage();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সেলসম্যান সম্পাদনা - <?php echo SITE_NAME; ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .logo h1 {
            font-size: 1.5rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .main-content {
            padding: 2rem 0;
        }
        .page-header {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .page-header h2 {
            margin-bottom: 0.5rem;
            color: #333;
        }
        .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }
        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }
        .edit-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        .edit-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
        }
        .card-header h3 {
            margin: 0;
        }
        .card-body {
            padding: 2rem;
        }
        .form-group {
            margin-bottom: 1.5rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-danger {
            background: #ff4757;
            color: white;
        }
        .btn-danger:hover {
            background: #ff3838;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
            font-weight: 500;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .back-link {
            margin-bottom: 2rem;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .back-link a:hover {
            text-decoration: underline;
        }
        .password-requirements {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .password-requirements h4 {
            color: #0066cc;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            color: #0066cc;
        }
        .password-requirements li {
            margin-bottom: 0.25rem;
        }
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            .edit-container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            .card-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1><i class="fas fa-shield-alt"></i> অ্যাডমিন প্যানেল</h1>
                </div>
                <div class="user-info">
                    <span><i class="fas fa-user"></i> <?php echo $_SESSION['admin_name']; ?></span>
                    <a href="dashboard.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                    </a>
                    <a href="logout.php" style="color: white; text-decoration: none;">
                        <i class="fas fa-sign-out-alt"></i> লগআউট
                    </a>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="back-link">
                <a href="salesman.php">
                    <i class="fas fa-arrow-left"></i> সেলসম্যান তালিকায় ফিরে যান
                </a>
            </div>

            <div class="page-header">
                <h2><i class="fas fa-user-edit"></i> সেলসম্যান সম্পাদনা</h2>
                <div class="breadcrumb">
                    <a href="dashboard.php">ড্যাশবোর্ড</a> / 
                    <a href="salesman.php">সেলসম্যান</a> / 
                    সম্পাদনা
                </div>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <div class="edit-container">
                <!-- Basic Information -->
                <div class="edit-card">
                    <div class="card-header">
                        <h3><i class="fas fa-user"></i> মূল তথ্য</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="update_info">
                            
                            <div class="form-group">
                                <label for="name">নাম *</label>
                                <input type="text" id="name" name="name" class="form-control" 
                                       value="<?php echo htmlspecialchars($salesman['name']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="email">ইমেইল *</label>
                                <input type="email" id="email" name="email" class="form-control" 
                                       value="<?php echo htmlspecialchars($salesman['email']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="phone">ফোন</label>
                                <input type="text" id="phone" name="phone" class="form-control" 
                                       value="<?php echo htmlspecialchars($salesman['phone']); ?>">
                            </div>

                            <div class="form-group">
                                <label for="employee_id">কর্মচারী আইডি *</label>
                                <input type="text" id="employee_id" name="employee_id" class="form-control" 
                                       value="<?php echo htmlspecialchars($salesman['employee_id']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="commission_rate">কমিশন রেট (%)</label>
                                <input type="number" id="commission_rate" name="commission_rate" class="form-control" 
                                       value="<?php echo $salesman['commission_rate']; ?>" step="0.01" min="0" max="100">
                            </div>

                            <div class="form-group">
                                <label for="status">স্ট্যাটাস</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="active" <?php echo $salesman['status'] == 'active' ? 'selected' : ''; ?>>সক্রিয়</option>
                                    <option value="inactive" <?php echo $salesman['status'] == 'inactive' ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> তথ্য আপডেট করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Password Change -->
                <div class="edit-card">
                    <div class="card-header">
                        <h3><i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন</h3>
                    </div>
                    <div class="card-body">
                        <div class="password-requirements">
                            <h4><i class="fas fa-info-circle"></i> পাসওয়ার্ড নির্দেশনা:</h4>
                            <ul>
                                <li>কমপক্ষে ৬ অক্ষরের হতে হবে</li>
                                <li>বড় ও ছোট হাতের অক্ষর ব্যবহার করুন</li>
                                <li>সংখ্যা ও বিশেষ চিহ্ন ব্যবহার করুন</li>
                            </ul>
                        </div>

                        <form method="POST">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="form-group">
                                <label for="new_password">নতুন পাসওয়ার্ড *</label>
                                <input type="password" id="new_password" name="new_password" 
                                       class="form-control" required minlength="6">
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">পাসওয়ার্ড নিশ্চিত করুন *</label>
                                <input type="password" id="confirm_password" name="confirm_password" 
                                       class="form-control" required minlength="6">
                            </div>

                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-key"></i> পাসওয়ার্ড পরিবর্তন করুন
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('পাসওয়ার্ড মিলছে না');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
