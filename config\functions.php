<?php
// Common Functions

// upload_image function is already defined in database.php

/**
 * Delete uploaded file
 */
function delete_uploaded_file($file_path) {
    if (file_exists($file_path)) {
        return unlink($file_path);
    }
    return false;
}

// format_price function is already defined in database.php

/**
 * Generate random string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * Validate Bangladesh mobile number
 */
function validate_bd_mobile($phone) {
    // Remove any spaces or dashes
    $phone = preg_replace('/[\s\-]/', '', $phone);
    
    // Check if it matches Bangladesh mobile pattern
    return preg_match('/^01[3-9]\d{8}$/', $phone);
}

/**
 * Format Bangladesh mobile number
 */
function format_bd_mobile($phone) {
    // Remove any spaces or dashes
    $phone = preg_replace('/[\s\-]/', '', $phone);
    
    // Add country code if not present
    if (strlen($phone) === 11 && substr($phone, 0, 2) === '01') {
        return '+880' . substr($phone, 1);
    }
    
    return $phone;
}

/**
 * Get file size in human readable format
 */
function human_filesize($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Truncate text with ellipsis
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Generate SEO friendly slug
 */
function generate_slug($text) {
    // Replace non-letter or digits by -
    $text = preg_replace('~[^\pL\d]+~u', '-', $text);
    
    // Transliterate
    $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
    
    // Remove unwanted characters
    $text = preg_replace('~[^-\w]+~', '', $text);
    
    // Trim
    $text = trim($text, '-');
    
    // Remove duplicate -
    $text = preg_replace('~-+~', '-', $text);
    
    // Lowercase
    $text = strtolower($text);
    
    if (empty($text)) {
        return 'n-a';
    }
    
    return $text;
}

/**
 * Check if user is admin
 */
function is_admin() {
    return isset($_SESSION['admin_id']);
}

/**
 * Check if user is customer
 */
function is_customer() {
    return isset($_SESSION['customer_id']);
}

/**
 * Get current user type
 */
function get_user_type() {
    if (is_admin()) {
        return 'admin';
    } elseif (is_customer()) {
        return 'customer';
    }
    return 'guest';
}

/**
 * Log activity
 */
function log_activity($action, $details = '', $user_id = null, $user_type = 'admin') {
    global $pdo;
    
    if (!$user_id) {
        $user_id = $_SESSION['admin_id'] ?? $_SESSION['customer_id'] ?? null;
    }
    
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, user_type, action, details, ip_address) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$user_id, $user_type, $action, $details, $ip_address]);
        return true;
    } catch(PDOException $e) {
        return false;
    }
}

/**
 * Get client IP address
 */
function get_client_ip() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * Send notification (placeholder for future implementation)
 */
function send_notification($type, $message, $user_id = null) {
    // This can be extended to send email, SMS, push notifications etc.
    return true;
}

/**
 * Calculate discount amount
 */
function calculate_discount($original_price, $discount_percentage) {
    return ($original_price * $discount_percentage) / 100;
}

/**
 * Calculate final price after discount
 */
function calculate_final_price($original_price, $discount_percentage) {
    $discount_amount = calculate_discount($original_price, $discount_percentage);
    return $original_price - $discount_amount;
}

/**
 * Check if string contains only Bengali characters
 */
function is_bengali($text) {
    return preg_match('/^[\x{0980}-\x{09FF}\s]+$/u', $text);
}

/**
 * Validate and sanitize phone number
 */
function sanitize_phone($phone) {
    // Remove all non-digit characters
    $phone = preg_replace('/\D/', '', $phone);
    
    // If starts with 880, remove it
    if (substr($phone, 0, 3) === '880') {
        $phone = '0' . substr($phone, 3);
    }
    
    return $phone;
}
?>
